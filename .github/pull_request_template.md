[Feature] [AIM2-622] Add manual statistics recalculation endpoint for succeeded portfolio items

## Description

**Context:** This PR implements a new REST endpoint that allows manual, retroactive calculation of statistics for closed deals (portfolio items with SUCCEED status). This addresses the need to recalculate statistics that may have been missed during automatic processing or need to be reprocessed for data consistency.

**Changes:**
1. **New REST Endpoint:** `POST /api/v1/business-base/portfolios/:portfolioId/recalculate-statistics`
   - Accepts portfolioId as path parameter
   - Validates customer ownership of the portfolio
   - Requires customer to have stats configuration
   - Processes all portfolio items with SUCCEED status
   - Returns processing summary with count and status message

2. **Enhanced Portfolio Use Case:** Added `recalculateStatisticsForSucceededItems()` method
   - Validates customer preferences and stats configuration
   - Retrieves all succeeded portfolio items for the specified portfolio
   - Calls existing `handleStatisticValuesExtraction()` for each item
   - Comprehensive error handling and logging with correlation IDs
   - Returns detailed processing results

3. **Comprehensive Test Coverage:** Added e2e tests covering:
   - Successful statistics recalculation scenarios
   - Error handling (404 for non-existent portfolio, 401 for unauthorized access)
   - Customer ownership validation
   - Edge cases and boundary conditions

**Technical Implementation:**
- Follows existing codebase patterns for authentication, validation, and error handling
- Includes complete Swagger/OpenAPI documentation with examples
- Implements proper HTTP status codes and response structures
- Uses existing `handleStatisticValuesExtraction()` method to maintain consistency
- Validates customer has required stats configuration before processing

**Business Value:** Enables manual intervention for statistics recalculation, improving data consistency and providing operational flexibility for handling edge cases where automatic processing may have failed.

## Type of Change

- [x] Feature: New functionality (non-breaking)
- [ ] Improvement: Minor enhancements or refactoring
- [ ] Bugfix: Non-breaking issue resolution
- [ ] Breaking Change: Alters existing behavior or APIs
- [ ] Security: Fixes vulnerabilities or improves security posture
- [ ] Deprecation: Marks features for removal
- [ ] Removal: Deletes deprecated or unused code
- [x] Logs/Monitoring: Adds or adjusts logging
- [ ] Repo Setup: Configuration, CI/CD, dependencies, etc.

## Checklist

- [x] Self-reviewed code
- [x] Added or adjusted E2E tests
- [ ] Tested in staging (for PRs to main branch)
- [x] Updated documentation (if applicable)
- [ ] Updated changelog (if necessary)

## Files Changed

### Core Implementation
- `src/business-base/application/use-cases/portfolio.use-case.ts`
  - Added `recalculateStatisticsForSucceededItems()` method
  - Enhanced validation to check customer stats configuration
  - Comprehensive error handling and logging

- `src/business-base/application/controllers/portfolio.controller.ts`
  - Added `POST /:portfolioId/recalculate-statistics` endpoint
  - Complete Swagger/OpenAPI documentation
  - Proper authentication and authorization

### Test Coverage
- `test/e2e/business-base/portfolio.controller.spec.ts`
  - Added comprehensive e2e test suite for new endpoint
  - Tests for success scenarios, error handling, and edge cases
  - Validation of response structure and status codes

## API Documentation

### New Endpoint
```
POST /api/v1/business-base/portfolios/{portfolioId}/recalculate-statistics
```

**Authentication:** Bearer JWT token required

**Parameters:**
- `portfolioId` (path): UUID of the portfolio to process

**Response Example:**
```json
{
  "statusCode": 200,
  "data": {
    "processedCount": 15,
    "message": "Successfully processed statistics for 15 succeeded portfolio items"
  }
}
```

**Error Responses:**
- `400`: Invalid portfolio ID format
- `401`: Unauthorized access
- `404`: Portfolio not found or no stats config
- `500`: Internal server error during processing

## Testing Instructions

1. **Prerequisites:**
   - Customer must have stats configuration in preferences
   - Portfolio must contain items with SUCCEED status

2. **Manual Testing:**
   ```bash
   curl -X POST \
     "http://localhost:3000/api/v1/business-base/portfolios/{portfolioId}/recalculate-statistics" \
     -H "Authorization: Bearer {jwt-token}" \
     -H "Content-Type: application/json"
   ```

3. **E2E Tests:**
   ```bash
   npm run test:e2e -- --testNamePattern="recalculate-statistics"
   ```

More information is available in [Confluence](https://edutalent.atlassian.net/wiki/spaces/EDUTALENT1/pages/32440321/Guidelines+for+contributions+in+our+repositories)
